'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { withAuth } from '@/hooks/useAuth';
import DashboardLayout from '@/components/layout/DashboardLayout';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import DataTable from '@/components/tables/DataTable';
import BudgetChart, { BudgetSummary } from '@/components/ui/BudgetChart';
import EmployeeAssignment from '@/components/ui/EmployeeAssignment';
import { DepartmentWithStats, EmployeeWithDepartment, TableColumn } from '@/types';
import { apiService } from '@/services/api';
import { formatCurrency, formatDate, getEmploymentStatusColor, getEmploymentTypeColor } from '@/lib/utils';
import { useToastHelpers } from '@/components/ui/Toast';

function DepartmentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { error } = useToastHelpers();
  
  const [department, setDepartment] = useState<DepartmentWithStats | null>(null);
  const [employees, setEmployees] = useState<EmployeeWithDepartment[]>([]);
  const [loading, setLoading] = useState(true);
  const [employeesLoading, setEmployeesLoading] = useState(false);

  const departmentId = params.id as string;

  const employeeColumns: TableColumn<EmployeeWithDepartment>[] = [
    {
      key: 'employee_id',
      label: 'Employee ID',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm font-medium">{value}</span>
      ),
    },
    {
      key: 'first_name',
      label: 'Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-blue-800">
              {row.first_name.charAt(0)}{row.last_name.charAt(0)}
            </span>
          </div>
          <div>
            <div className="font-medium text-gray-900">
              {row.first_name} {row.last_name}
            </div>
            <div className="text-sm text-gray-500">{row.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'position',
      label: 'Position',
      sortable: true,
    },
    {
      key: 'employment_type',
      label: 'Type',
      sortable: true,
      render: (value) => (
        <Badge 
          variant="default" 
          size="sm"
          className={getEmploymentTypeColor(value)}
        >
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'employment_status',
      label: 'Status',
      sortable: true,
      render: (value) => (
        <Badge 
          variant="default" 
          size="sm"
          className={getEmploymentStatusColor(value)}
        >
          {value.toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'hire_date',
      label: 'Hire Date',
      sortable: true,
      render: (value) => formatDate(value),
    },
    {
      key: 'salary',
      label: 'Salary',
      sortable: true,
      render: (value) => value ? formatCurrency(value) : '-',
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_, row) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => router.push(`/employees/${row.id}`)}
          >
            View
          </Button>
        </div>
      ),
    },
  ];

  useEffect(() => {
    if (departmentId) {
      fetchDepartment();
      fetchDepartmentEmployees();
    }
  }, [departmentId]);

  const fetchDepartment = async () => {
    try {
      setLoading(true);
      const response = await apiService.getDepartment(Number(departmentId));
      setDepartment(response.department);
    } catch (err) {
      console.error('Failed to fetch department:', err);
      error('Error', 'Failed to load department details');
      router.push('/departments');
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartmentEmployees = async () => {
    try {
      setEmployeesLoading(true);
      const response = await apiService.getDepartmentEmployees(Number(departmentId));
      setEmployees(response.employees || []);
    } catch (err) {
      console.error('Failed to fetch department employees:', err);
      error('Error', 'Failed to load department employees');
    } finally {
      setEmployeesLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/departments/${departmentId}/edit`);
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this department? This action cannot be undone.')) {
      return;
    }

    try {
      await apiService.deleteDepartment(Number(departmentId));
      router.push('/departments');
    } catch (err) {
      console.error('Failed to delete department:', err);
      error('Error', 'Failed to delete department');
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="Department Details">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!department) {
    return (
      <DashboardLayout title="Department Not Found">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Department Not Found</h2>
          <p className="text-gray-600 mb-6">The department you're looking for doesn't exist.</p>
          <Button onClick={() => router.push('/departments')}>
            Back to Departments
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title={department.name}>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{department.name}</h1>
            <p className="text-gray-600">Department Code: {department.code}</p>
            {department.description && (
              <p className="text-gray-600 mt-2">{department.description}</p>
            )}
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleEdit}>
              Edit
            </Button>
            <Button variant="danger" onClick={handleDelete}>
              Delete
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{department.total_employees}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Employees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{department.active_employees}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Salary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {department.average_salary ? formatCurrency(department.average_salary) : 'N/A'}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {formatCurrency(department.budget)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Department Information */}
        <Card>
          <CardHeader>
            <CardTitle>Department Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500">Department Head</label>
                <p className="mt-1 text-sm text-gray-900">
                  {department.department_head_name || 'Not assigned'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Location</label>
                <p className="mt-1 text-sm text-gray-900">
                  {department.location || 'Not specified'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Phone</label>
                <p className="mt-1 text-sm text-gray-900">
                  {department.phone ? (
                    <a href={`tel:${department.phone}`} className="text-blue-600 hover:underline">
                      {department.phone}
                    </a>
                  ) : 'Not specified'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Email</label>
                <p className="mt-1 text-sm text-gray-900">
                  {department.email ? (
                    <a href={`mailto:${department.email}`} className="text-blue-600 hover:underline">
                      {department.email}
                    </a>
                  ) : 'Not specified'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Status</label>
                <Badge 
                  variant={department.is_active ? "success" : "danger"} 
                  size="sm"
                  className="mt-1"
                >
                  {department.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Budget Tracking */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <BudgetChart
            data={{
              allocated: department.budget,
              spent: department.budget * 0.65, // Mock data - replace with actual spent amount
              remaining: department.budget * 0.35,
              categories: [
                { name: 'Salaries', allocated: department.budget * 0.7, spent: department.budget * 0.65 },
                { name: 'Equipment', allocated: department.budget * 0.2, spent: department.budget * 0.15 },
                { name: 'Supplies', allocated: department.budget * 0.1, spent: department.budget * 0.08 },
              ]
            }}
            title="Department Budget"
          />

          <Card>
            <CardHeader>
              <CardTitle>Budget Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <BudgetSummary
                  allocated={department.budget}
                  spent={department.budget * 0.65}
                />

                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div>
                    <p className="text-sm text-gray-500">Monthly Average</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(department.budget / 12)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Per Employee</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {department.total_employees > 0
                        ? formatCurrency(department.budget / department.total_employees)
                        : formatCurrency(0)
                      }
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Employee Assignment */}
        <EmployeeAssignment
          departmentId={Number(departmentId)}
          onAssignmentChange={() => {
            fetchDepartment();
            fetchDepartmentEmployees();
          }}
        />

        {/* Department Employees */}
        <Card>
          <CardHeader>
            <CardTitle>Department Employees</CardTitle>
          </CardHeader>
          <CardContent>
            <DataTable
              data={employees}
              columns={employeeColumns}
              loading={employeesLoading}
            />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

export default withAuth(DepartmentDetailPage, ['admin', 'hr', 'department_head']);
