"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/departments/[id]/page",{

/***/ "(app-pages-browser)/./src/components/ui/EmployeeAssignment.tsx":
/*!**************************************************!*\
  !*** ./src/components/ui/EmployeeAssignment.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _Badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _Modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Modal */ \"(app-pages-browser)/./src/components/ui/Modal.tsx\");\n/* harmony import */ var _Toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* harmony import */ var _DepartmentHierarchy__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DepartmentHierarchy */ \"(app-pages-browser)/./src/components/ui/DepartmentHierarchy.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst EmployeeAssignment = (param)=>{\n    let { departmentId, onAssignmentChange, className = \"\" } = param;\n    var _this = undefined;\n    _s();\n    const { success, error } = (0,_Toast__WEBPACK_IMPORTED_MODULE_6__.useToastHelpers)();\n    const [employees, setEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unassignedEmployees, setUnassignedEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedEmployees, setSelectedEmployees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAssignModal, setShowAssignModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showReassignModal, setShowReassignModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetDepartmentId, setTargetDepartmentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmployeeAssignment.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"EmployeeAssignment.useEffect\"], [\n        departmentId\n    ]);\n    const fetchData = async ()=>{\n        try {\n            setLoading(true);\n            const [employeesRes, unassignedRes, departmentsRes] = await Promise.all([\n                _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getDepartmentEmployees(departmentId),\n                _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getEmployees({\n                    department_id: 'null',\n                    limit: 100\n                }),\n                _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getDepartments({\n                    limit: 100\n                })\n            ]);\n            setEmployees(employeesRes.employees || []);\n            setUnassignedEmployees(unassignedRes.employees || []);\n            setDepartments(departmentsRes.departments || []);\n        } catch (err) {\n            console.error('Failed to fetch data:', err);\n            error('Error', 'Failed to load employee assignment data');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSelectEmployee = (employeeId)=>{\n        setSelectedEmployees((prev)=>prev.includes(employeeId) ? prev.filter((id)=>id !== employeeId) : [\n                ...prev,\n                employeeId\n            ]);\n    };\n    const handleSelectAll = (employeeList)=>{\n        const allIds = employeeList.map((emp)=>emp.id);\n        if (selectedEmployees.length === allIds.length) {\n            setSelectedEmployees([]);\n        } else {\n            setSelectedEmployees(allIds);\n        }\n    };\n    const handleAssignEmployees = async ()=>{\n        try {\n            setActionLoading(true);\n            await Promise.all(selectedEmployees.map((id)=>_services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.updateEmployee(id, {\n                    department_id: departmentId\n                })));\n            success('Success', \"\".concat(selectedEmployees.length, \" employees assigned successfully\"));\n            setSelectedEmployees([]);\n            setShowAssignModal(false);\n            fetchData();\n            onAssignmentChange === null || onAssignmentChange === void 0 ? void 0 : onAssignmentChange();\n        } catch (err) {\n            console.error('Failed to assign employees:', err);\n            error('Error', 'Failed to assign employees');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleReassignEmployees = async ()=>{\n        if (!targetDepartmentId) return;\n        try {\n            setActionLoading(true);\n            await Promise.all(selectedEmployees.map((id)=>_services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.updateEmployee(id, {\n                    department_id: targetDepartmentId\n                })));\n            success('Success', \"\".concat(selectedEmployees.length, \" employees reassigned successfully\"));\n            setSelectedEmployees([]);\n            setShowReassignModal(false);\n            setTargetDepartmentId(undefined);\n            fetchData();\n            onAssignmentChange === null || onAssignmentChange === void 0 ? void 0 : onAssignmentChange();\n        } catch (err) {\n            console.error('Failed to reassign employees:', err);\n            error('Error', 'Failed to reassign employees');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const renderEmployeeList = function(employeeList, title) {\n        let showActions = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, _this),\n                            showActions && employeeList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: selectedEmployees.length === employeeList.length && employeeList.length > 0,\n                                        onChange: ()=>handleSelectAll(employeeList),\n                                        className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Select All\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, _this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 7\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-64 overflow-y-auto\",\n                    children: employeeList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 text-center text-gray-500\",\n                        children: \"No employees found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y\",\n                        children: employeeList.map((employee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 flex items-center justify-between hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedEmployees.includes(employee.id),\n                                                onChange: ()=>handleSelectEmployee(employee.id),\n                                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center overflow-hidden\",\n                                                children: employee.photo_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: employee.photo_url,\n                                                    alt: \"\".concat(employee.first_name, \" \").concat(employee.last_name),\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 23\n                                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: [\n                                                        employee.first_name.charAt(0),\n                                                        employee.last_name.charAt(0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: [\n                                                            employee.first_name,\n                                                            \" \",\n                                                            employee.last_name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: employee.position\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Badge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: employee.employment_status === 'active' ? 'success' : 'warning',\n                                            size: \"sm\",\n                                            children: employee.employment_status.toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, employee.id, true, {\n                                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, _this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, _this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 7\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n            lineNumber: 121,\n            columnNumber: 5\n        }, _this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"Employee Assignment\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: selectedEmployees.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowReassignModal(true),\n                                    disabled: actionLoading,\n                                    children: [\n                                        \"Reassign Selected (\",\n                                        selectedEmployees.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onClick: ()=>setShowAssignModal(true),\n                                    disabled: actionLoading,\n                                    children: [\n                                        \"Assign Selected (\",\n                                        selectedEmployees.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    renderEmployeeList(employees, \"Current Department Employees (\".concat(employees.length, \")\")),\n                    renderEmployeeList(unassignedEmployees, \"Unassigned Employees (\".concat(unassignedEmployees.length, \")\"))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Modal__WEBPACK_IMPORTED_MODULE_5__.ConfirmationModal, {\n                isOpen: showAssignModal,\n                onClose: ()=>setShowAssignModal(false),\n                onConfirm: handleAssignEmployees,\n                title: \"Assign Employees\",\n                message: \"Are you sure you want to assign \".concat(selectedEmployees.length, \" selected employees to this department?\"),\n                confirmText: \"Assign\",\n                type: \"info\",\n                loading: actionLoading\n            }, void 0, false, {\n                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            showReassignModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-gray-500 bg-opacity-75\",\n                            onClick: ()=>setShowReassignModal(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900 mb-4\",\n                                            children: \"Reassign Employees\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mb-4\",\n                                            children: [\n                                                \"Select the target department for \",\n                                                selectedEmployees.length,\n                                                \" selected employees:\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentHierarchy__WEBPACK_IMPORTED_MODULE_7__.DepartmentTreeSelect, {\n                                            departments: departments.filter((d)=>d.id !== departmentId),\n                                            selectedId: targetDepartmentId,\n                                            onSelect: (dept)=>setTargetDepartmentId(dept === null || dept === void 0 ? void 0 : dept.id),\n                                            placeholder: \"Select Target Department\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onClick: handleReassignEmployees,\n                                            disabled: !targetDepartmentId || actionLoading,\n                                            className: \"w-full sm:w-auto sm:ml-3\",\n                                            children: actionLoading ? 'Reassigning...' : 'Reassign'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            variant: \"outline\",\n                                            onClick: ()=>{\n                                                setShowReassignModal(false);\n                                                setTargetDepartmentId(undefined);\n                                            },\n                                            disabled: actionLoading,\n                                            className: \"mt-3 w-full sm:mt-0 sm:w-auto\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\manajemen-karyawan\\\\frontend\\\\src\\\\components\\\\ui\\\\EmployeeAssignment.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EmployeeAssignment, \"fiREEYtnAqBV/JNwNIfd4PAK+9M=\", false, function() {\n    return [\n        _Toast__WEBPACK_IMPORTED_MODULE_6__.useToastHelpers\n    ];\n});\n_c = EmployeeAssignment;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EmployeeAssignment);\nvar _c;\n$RefreshReg$(_c, \"EmployeeAssignment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL0VtcGxveWVlQXNzaWdubWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBRVA7QUFDZDtBQUNGO0FBQ2dCO0FBQ0Y7QUFDbUI7QUFRN0QsTUFBTVMscUJBQXdEO1FBQUMsRUFDN0RDLFlBQVksRUFDWkMsa0JBQWtCLEVBQ2xCQyxZQUFZLEVBQUUsRUFDZjs7O0lBQ0MsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLEtBQUssRUFBRSxHQUFHUCx1REFBZUE7SUFDMUMsTUFBTSxDQUFDUSxXQUFXQyxhQUFhLEdBQUdmLCtDQUFRQSxDQUEyQixFQUFFO0lBQ3ZFLE1BQU0sQ0FBQ2dCLHFCQUFxQkMsdUJBQXVCLEdBQUdqQiwrQ0FBUUEsQ0FBMkIsRUFBRTtJQUMzRixNQUFNLENBQUNrQixhQUFhQyxlQUFlLEdBQUduQiwrQ0FBUUEsQ0FBd0IsRUFBRTtJQUN4RSxNQUFNLENBQUNvQixTQUFTQyxXQUFXLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNzQixtQkFBbUJDLHFCQUFxQixHQUFHdkIsK0NBQVFBLENBQVcsRUFBRTtJQUN2RSxNQUFNLENBQUN3QixpQkFBaUJDLG1CQUFtQixHQUFHekIsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDMEIsbUJBQW1CQyxxQkFBcUIsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzRCLG9CQUFvQkMsc0JBQXNCLEdBQUc3QiwrQ0FBUUE7SUFDNUQsTUFBTSxDQUFDOEIsZUFBZUMsaUJBQWlCLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUVuREMsZ0RBQVNBO3dDQUFDO1lBQ1IrQjtRQUNGO3VDQUFHO1FBQUN2QjtLQUFhO0lBRWpCLE1BQU11QixZQUFZO1FBQ2hCLElBQUk7WUFDRlgsV0FBVztZQUNYLE1BQU0sQ0FBQ1ksY0FBY0MsZUFBZUMsZUFBZSxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztnQkFDdEVuQyxxREFBVUEsQ0FBQ29DLHNCQUFzQixDQUFDN0I7Z0JBQ2xDUCxxREFBVUEsQ0FBQ3FDLFlBQVksQ0FBQztvQkFBRUMsZUFBZTtvQkFBUUMsT0FBTztnQkFBSTtnQkFDNUR2QyxxREFBVUEsQ0FBQ3dDLGNBQWMsQ0FBQztvQkFBRUQsT0FBTztnQkFBSTthQUN4QztZQUVEMUIsYUFBYSxhQUFzQkQsU0FBUyxJQUFJLEVBQUU7WUFDbERHLHVCQUF1QixjQUF1QkgsU0FBUyxJQUFJLEVBQUU7WUFDN0RLLGVBQWUsZUFBd0JELFdBQVcsSUFBSSxFQUFFO1FBQzFELEVBQUUsT0FBT3lCLEtBQUs7WUFDWkMsUUFBUS9CLEtBQUssQ0FBQyx5QkFBeUI4QjtZQUN2QzlCLE1BQU0sU0FBUztRQUNqQixTQUFVO1lBQ1JRLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXdCLHVCQUF1QixDQUFDQztRQUM1QnZCLHFCQUFxQndCLENBQUFBLE9BQ25CQSxLQUFLQyxRQUFRLENBQUNGLGNBQ1ZDLEtBQUtFLE1BQU0sQ0FBQ0MsQ0FBQUEsS0FBTUEsT0FBT0osY0FDekI7bUJBQUlDO2dCQUFNRDthQUFXO0lBRTdCO0lBRUEsTUFBTUssa0JBQWtCLENBQUNDO1FBQ3ZCLE1BQU1DLFNBQVNELGFBQWFFLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUwsRUFBRTtRQUM3QyxJQUFJNUIsa0JBQWtCa0MsTUFBTSxLQUFLSCxPQUFPRyxNQUFNLEVBQUU7WUFDOUNqQyxxQkFBcUIsRUFBRTtRQUN6QixPQUFPO1lBQ0xBLHFCQUFxQjhCO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNSSx3QkFBd0I7UUFDNUIsSUFBSTtZQUNGMUIsaUJBQWlCO1lBQ2pCLE1BQU1LLFFBQVFDLEdBQUcsQ0FDZmYsa0JBQWtCZ0MsR0FBRyxDQUFDSixDQUFBQSxLQUNwQmhELHFEQUFVQSxDQUFDd0QsY0FBYyxDQUFDUixJQUFJO29CQUFFVixlQUFlL0I7Z0JBQWE7WUFHaEVHLFFBQVEsV0FBVyxHQUE0QixPQUF6QlUsa0JBQWtCa0MsTUFBTSxFQUFDO1lBQy9DakMscUJBQXFCLEVBQUU7WUFDdkJFLG1CQUFtQjtZQUNuQk87WUFDQXRCLCtCQUFBQSx5Q0FBQUE7UUFDRixFQUFFLE9BQU9pQyxLQUFLO1lBQ1pDLFFBQVEvQixLQUFLLENBQUMsK0JBQStCOEI7WUFDN0M5QixNQUFNLFNBQVM7UUFDakIsU0FBVTtZQUNSa0IsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNNEIsMEJBQTBCO1FBQzlCLElBQUksQ0FBQy9CLG9CQUFvQjtRQUV6QixJQUFJO1lBQ0ZHLGlCQUFpQjtZQUNqQixNQUFNSyxRQUFRQyxHQUFHLENBQ2ZmLGtCQUFrQmdDLEdBQUcsQ0FBQ0osQ0FBQUEsS0FDcEJoRCxxREFBVUEsQ0FBQ3dELGNBQWMsQ0FBQ1IsSUFBSTtvQkFBRVYsZUFBZVo7Z0JBQW1CO1lBR3RFaEIsUUFBUSxXQUFXLEdBQTRCLE9BQXpCVSxrQkFBa0JrQyxNQUFNLEVBQUM7WUFDL0NqQyxxQkFBcUIsRUFBRTtZQUN2QkkscUJBQXFCO1lBQ3JCRSxzQkFBc0IrQjtZQUN0QjVCO1lBQ0F0QiwrQkFBQUEseUNBQUFBO1FBQ0YsRUFBRSxPQUFPaUMsS0FBSztZQUNaQyxRQUFRL0IsS0FBSyxDQUFDLGlDQUFpQzhCO1lBQy9DOUIsTUFBTSxTQUFTO1FBQ2pCLFNBQVU7WUFDUmtCLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTThCLHFCQUFxQixTQUFDVCxjQUF3Q1U7WUFBZUMsK0VBQXVCOzZCQUN4Ryw4REFBQ0M7WUFBSXJELFdBQVU7OzhCQUNiLDhEQUFDcUQ7b0JBQUlyRCxXQUFVOzhCQUNiLDRFQUFDcUQ7d0JBQUlyRCxXQUFVOzswQ0FDYiw4REFBQ3NEO2dDQUFHdEQsV0FBVTswQ0FBcUNtRDs7Ozs7OzRCQUNsREMsZUFBZVgsYUFBYUksTUFBTSxHQUFHLG1CQUNwQyw4REFBQ1E7Z0NBQUlyRCxXQUFVOztrREFDYiw4REFBQ3VEO3dDQUNDQyxNQUFLO3dDQUNMQyxTQUFTOUMsa0JBQWtCa0MsTUFBTSxLQUFLSixhQUFhSSxNQUFNLElBQUlKLGFBQWFJLE1BQU0sR0FBRzt3Q0FDbkZhLFVBQVUsSUFBTWxCLGdCQUFnQkM7d0NBQ2hDekMsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDMkQ7d0NBQUszRCxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTWhELDhEQUFDcUQ7b0JBQUlyRCxXQUFVOzhCQUNaeUMsYUFBYUksTUFBTSxLQUFLLGtCQUN2Qiw4REFBQ1E7d0JBQUlyRCxXQUFVO2tDQUFnQzs7Ozs7OENBSS9DLDhEQUFDcUQ7d0JBQUlyRCxXQUFVO2tDQUNaeUMsYUFBYUUsR0FBRyxDQUFDaUIsQ0FBQUEseUJBQ2hCLDhEQUFDUDtnQ0FBc0JyRCxXQUFVOztrREFDL0IsOERBQUNxRDt3Q0FBSXJELFdBQVU7OzRDQUNab0QsNkJBQ0MsOERBQUNHO2dEQUNDQyxNQUFLO2dEQUNMQyxTQUFTOUMsa0JBQWtCMEIsUUFBUSxDQUFDdUIsU0FBU3JCLEVBQUU7Z0RBQy9DbUIsVUFBVSxJQUFNeEIscUJBQXFCMEIsU0FBU3JCLEVBQUU7Z0RBQ2hEdkMsV0FBVTs7Ozs7OzBEQUdkLDhEQUFDcUQ7Z0RBQUlyRCxXQUFVOzBEQUNaNEQsU0FBU0MsU0FBUyxpQkFDakIsOERBQUNDO29EQUNDQyxLQUFLSCxTQUFTQyxTQUFTO29EQUN2QkcsS0FBSyxHQUEwQkosT0FBdkJBLFNBQVNLLFVBQVUsRUFBQyxLQUFzQixPQUFuQkwsU0FBU00sU0FBUztvREFDakRsRSxXQUFVOzs7OzswRUFHWiw4REFBQzJEO29EQUFLM0QsV0FBVTs7d0RBQ2I0RCxTQUFTSyxVQUFVLENBQUNFLE1BQU0sQ0FBQzt3REFBSVAsU0FBU00sU0FBUyxDQUFDQyxNQUFNLENBQUM7Ozs7Ozs7Ozs7OzswREFJaEUsOERBQUNkOztrRUFDQyw4REFBQ2U7d0RBQUVwRSxXQUFVOzs0REFDVjRELFNBQVNLLFVBQVU7NERBQUM7NERBQUVMLFNBQVNNLFNBQVM7Ozs7Ozs7a0VBRTNDLDhEQUFDRTt3REFBRXBFLFdBQVU7a0VBQXlCNEQsU0FBU1MsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUkzRCw4REFBQ2hCO3dDQUFJckQsV0FBVTtrREFDYiw0RUFBQ1AsOENBQUtBOzRDQUNKNkUsU0FBU1YsU0FBU1csaUJBQWlCLEtBQUssV0FBVyxZQUFZOzRDQUMvREMsTUFBSztzREFFSlosU0FBU1csaUJBQWlCLENBQUNFLFdBQVc7Ozs7Ozs7Ozs7OzsrQkFwQ25DYixTQUFTckIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQStDakMsSUFBSTlCLFNBQVM7UUFDWCxxQkFDRSw4REFBQzRDO1lBQUlyRCxXQUFXLHlDQUFtRCxPQUFWQTtzQkFDdkQsNEVBQUNxRDtnQkFBSXJELFdBQVU7Ozs7Ozs7Ozs7O0lBR3JCO0lBRUEscUJBQ0UsOERBQUNxRDtRQUFJckQsV0FBVyxhQUF1QixPQUFWQTs7MEJBQzNCLDhEQUFDcUQ7Z0JBQUlyRCxXQUFVOztrQ0FDYiw4REFBQzBFO3dCQUFHMUUsV0FBVTtrQ0FBc0M7Ozs7OztrQ0FDcEQsOERBQUNxRDt3QkFBSXJELFdBQVU7a0NBQ1pXLGtCQUFrQmtDLE1BQU0sR0FBRyxtQkFDMUI7OzhDQUNFLDhEQUFDckQsK0NBQU1BO29DQUNMOEUsU0FBUTtvQ0FDUkssU0FBUyxJQUFNM0QscUJBQXFCO29DQUNwQzRELFVBQVV6RDs7d0NBQ1g7d0NBQ3FCUixrQkFBa0JrQyxNQUFNO3dDQUFDOzs7Ozs7OzhDQUUvQyw4REFBQ3JELCtDQUFNQTtvQ0FDTG1GLFNBQVMsSUFBTTdELG1CQUFtQjtvQ0FDbEM4RCxVQUFVekQ7O3dDQUNYO3dDQUNtQlIsa0JBQWtCa0MsTUFBTTt3Q0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3JELDhEQUFDUTtnQkFBSXJELFdBQVU7O29CQUNaa0QsbUJBQW1CL0MsV0FBVyxpQ0FBa0QsT0FBakJBLFVBQVUwQyxNQUFNLEVBQUM7b0JBQ2hGSyxtQkFBbUI3QyxxQkFBcUIseUJBQW9ELE9BQTNCQSxvQkFBb0J3QyxNQUFNLEVBQUM7Ozs7Ozs7MEJBSS9GLDhEQUFDbkQscURBQWlCQTtnQkFDaEJtRixRQUFRaEU7Z0JBQ1JpRSxTQUFTLElBQU1oRSxtQkFBbUI7Z0JBQ2xDaUUsV0FBV2pDO2dCQUNYSyxPQUFNO2dCQUNONkIsU0FBUyxtQ0FBNEQsT0FBekJyRSxrQkFBa0JrQyxNQUFNLEVBQUM7Z0JBQ3JFb0MsYUFBWTtnQkFDWnpCLE1BQUs7Z0JBQ0wvQyxTQUFTVTs7Ozs7O1lBSVZKLG1DQUNDLDhEQUFDc0M7Z0JBQUlyRCxXQUFVOzBCQUNiLDRFQUFDcUQ7b0JBQUlyRCxXQUFVOztzQ0FDYiw4REFBQ3FEOzRCQUFJckQsV0FBVTs0QkFBMEMyRSxTQUFTLElBQU0zRCxxQkFBcUI7Ozs7OztzQ0FDN0YsOERBQUNxQzs0QkFBSXJELFdBQVU7OzhDQUNiLDhEQUFDcUQ7b0NBQUlyRCxXQUFVOztzREFDYiw4REFBQ3NEOzRDQUFHdEQsV0FBVTtzREFBbUQ7Ozs7OztzREFHakUsOERBQUNvRTs0Q0FBRXBFLFdBQVU7O2dEQUE2QjtnREFDTlcsa0JBQWtCa0MsTUFBTTtnREFBQzs7Ozs7OztzREFFN0QsOERBQUNqRCxzRUFBb0JBOzRDQUNuQlcsYUFBYUEsWUFBWStCLE1BQU0sQ0FBQzRDLENBQUFBLElBQUtBLEVBQUUzQyxFQUFFLEtBQUt6Qzs0Q0FDOUNxRixZQUFZbEU7NENBQ1ptRSxVQUFVLENBQUNDLE9BQVNuRSxzQkFBc0JtRSxpQkFBQUEsMkJBQUFBLEtBQU05QyxFQUFFOzRDQUNsRCtDLGFBQVk7Ozs7Ozs7Ozs7Ozs4Q0FHaEIsOERBQUNqQztvQ0FBSXJELFdBQVU7O3NEQUNiLDhEQUFDUiwrQ0FBTUE7NENBQ0xtRixTQUFTM0I7NENBQ1Q0QixVQUFVLENBQUMzRCxzQkFBc0JFOzRDQUNqQ25CLFdBQVU7c0RBRVRtQixnQkFBZ0IsbUJBQW1COzs7Ozs7c0RBRXRDLDhEQUFDM0IsK0NBQU1BOzRDQUNMOEUsU0FBUTs0Q0FDUkssU0FBUztnREFDUDNELHFCQUFxQjtnREFDckJFLHNCQUFzQitCOzRDQUN4Qjs0Q0FDQTJCLFVBQVV6RDs0Q0FDVm5CLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWpCO0dBaFJNSDs7UUFLdUJGLG1EQUFlQTs7O0tBTHRDRTtBQWtSTixpRUFBZUEsa0JBQWtCQSxFQUFDIiwic291cmNlcyI6WyJEOlxcbWFuYWplbWVuLWthcnlhd2FuXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcRW1wbG95ZWVBc3NpZ25tZW50LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgRW1wbG95ZWVXaXRoRGVwYXJ0bWVudCwgRGVwYXJ0bWVudFdpdGhTdGF0cyB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgYXBpU2VydmljZSB9IGZyb20gJ0Avc2VydmljZXMvYXBpJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi9CdXR0b24nO1xuaW1wb3J0IEJhZGdlIGZyb20gJy4vQmFkZ2UnO1xuaW1wb3J0IHsgQ29uZmlybWF0aW9uTW9kYWwgfSBmcm9tICcuL01vZGFsJztcbmltcG9ydCB7IHVzZVRvYXN0SGVscGVycyB9IGZyb20gJy4vVG9hc3QnO1xuaW1wb3J0IHsgRGVwYXJ0bWVudFRyZWVTZWxlY3QgfSBmcm9tICcuL0RlcGFydG1lbnRIaWVyYXJjaHknO1xuXG5pbnRlcmZhY2UgRW1wbG95ZWVBc3NpZ25tZW50UHJvcHMge1xuICBkZXBhcnRtZW50SWQ6IG51bWJlcjtcbiAgb25Bc3NpZ25tZW50Q2hhbmdlPzogKCkgPT4gdm9pZDtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5jb25zdCBFbXBsb3llZUFzc2lnbm1lbnQ6IFJlYWN0LkZDPEVtcGxveWVlQXNzaWdubWVudFByb3BzPiA9ICh7XG4gIGRlcGFydG1lbnRJZCxcbiAgb25Bc3NpZ25tZW50Q2hhbmdlLFxuICBjbGFzc05hbWUgPSBcIlwiXG59KSA9PiB7XG4gIGNvbnN0IHsgc3VjY2VzcywgZXJyb3IgfSA9IHVzZVRvYXN0SGVscGVycygpO1xuICBjb25zdCBbZW1wbG95ZWVzLCBzZXRFbXBsb3llZXNdID0gdXNlU3RhdGU8RW1wbG95ZWVXaXRoRGVwYXJ0bWVudFtdPihbXSk7XG4gIGNvbnN0IFt1bmFzc2lnbmVkRW1wbG95ZWVzLCBzZXRVbmFzc2lnbmVkRW1wbG95ZWVzXSA9IHVzZVN0YXRlPEVtcGxveWVlV2l0aERlcGFydG1lbnRbXT4oW10pO1xuICBjb25zdCBbZGVwYXJ0bWVudHMsIHNldERlcGFydG1lbnRzXSA9IHVzZVN0YXRlPERlcGFydG1lbnRXaXRoU3RhdHNbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3NlbGVjdGVkRW1wbG95ZWVzLCBzZXRTZWxlY3RlZEVtcGxveWVlc10gPSB1c2VTdGF0ZTxudW1iZXJbXT4oW10pO1xuICBjb25zdCBbc2hvd0Fzc2lnbk1vZGFsLCBzZXRTaG93QXNzaWduTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd1JlYXNzaWduTW9kYWwsIHNldFNob3dSZWFzc2lnbk1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3RhcmdldERlcGFydG1lbnRJZCwgc2V0VGFyZ2V0RGVwYXJ0bWVudElkXSA9IHVzZVN0YXRlPG51bWJlciB8IHVuZGVmaW5lZD4oKTtcbiAgY29uc3QgW2FjdGlvbkxvYWRpbmcsIHNldEFjdGlvbkxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hEYXRhKCk7XG4gIH0sIFtkZXBhcnRtZW50SWRdKTtcblxuICBjb25zdCBmZXRjaERhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCBbZW1wbG95ZWVzUmVzLCB1bmFzc2lnbmVkUmVzLCBkZXBhcnRtZW50c1Jlc10gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgIGFwaVNlcnZpY2UuZ2V0RGVwYXJ0bWVudEVtcGxveWVlcyhkZXBhcnRtZW50SWQpLFxuICAgICAgICBhcGlTZXJ2aWNlLmdldEVtcGxveWVlcyh7IGRlcGFydG1lbnRfaWQ6ICdudWxsJywgbGltaXQ6IDEwMCB9KSxcbiAgICAgICAgYXBpU2VydmljZS5nZXREZXBhcnRtZW50cyh7IGxpbWl0OiAxMDAgfSlcbiAgICAgIF0pO1xuXG4gICAgICBzZXRFbXBsb3llZXMoKGVtcGxveWVlc1JlcyBhcyBhbnkpLmVtcGxveWVlcyB8fCBbXSk7XG4gICAgICBzZXRVbmFzc2lnbmVkRW1wbG95ZWVzKCh1bmFzc2lnbmVkUmVzIGFzIGFueSkuZW1wbG95ZWVzIHx8IFtdKTtcbiAgICAgIHNldERlcGFydG1lbnRzKChkZXBhcnRtZW50c1JlcyBhcyBhbnkpLmRlcGFydG1lbnRzIHx8IFtdKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBkYXRhOicsIGVycik7XG4gICAgICBlcnJvcignRXJyb3InLCAnRmFpbGVkIHRvIGxvYWQgZW1wbG95ZWUgYXNzaWdubWVudCBkYXRhJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVTZWxlY3RFbXBsb3llZSA9IChlbXBsb3llZUlkOiBudW1iZXIpID0+IHtcbiAgICBzZXRTZWxlY3RlZEVtcGxveWVlcyhwcmV2ID0+IFxuICAgICAgcHJldi5pbmNsdWRlcyhlbXBsb3llZUlkKSBcbiAgICAgICAgPyBwcmV2LmZpbHRlcihpZCA9PiBpZCAhPT0gZW1wbG95ZWVJZClcbiAgICAgICAgOiBbLi4ucHJldiwgZW1wbG95ZWVJZF1cbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNlbGVjdEFsbCA9IChlbXBsb3llZUxpc3Q6IEVtcGxveWVlV2l0aERlcGFydG1lbnRbXSkgPT4ge1xuICAgIGNvbnN0IGFsbElkcyA9IGVtcGxveWVlTGlzdC5tYXAoZW1wID0+IGVtcC5pZCk7XG4gICAgaWYgKHNlbGVjdGVkRW1wbG95ZWVzLmxlbmd0aCA9PT0gYWxsSWRzLmxlbmd0aCkge1xuICAgICAgc2V0U2VsZWN0ZWRFbXBsb3llZXMoW10pO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRTZWxlY3RlZEVtcGxveWVlcyhhbGxJZHMpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVBc3NpZ25FbXBsb3llZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcodHJ1ZSk7XG4gICAgICBhd2FpdCBQcm9taXNlLmFsbChcbiAgICAgICAgc2VsZWN0ZWRFbXBsb3llZXMubWFwKGlkID0+IFxuICAgICAgICAgIGFwaVNlcnZpY2UudXBkYXRlRW1wbG95ZWUoaWQsIHsgZGVwYXJ0bWVudF9pZDogZGVwYXJ0bWVudElkIH0pXG4gICAgICAgIClcbiAgICAgICk7XG4gICAgICBzdWNjZXNzKCdTdWNjZXNzJywgYCR7c2VsZWN0ZWRFbXBsb3llZXMubGVuZ3RofSBlbXBsb3llZXMgYXNzaWduZWQgc3VjY2Vzc2Z1bGx5YCk7XG4gICAgICBzZXRTZWxlY3RlZEVtcGxveWVlcyhbXSk7XG4gICAgICBzZXRTaG93QXNzaWduTW9kYWwoZmFsc2UpO1xuICAgICAgZmV0Y2hEYXRhKCk7XG4gICAgICBvbkFzc2lnbm1lbnRDaGFuZ2U/LigpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGFzc2lnbiBlbXBsb3llZXM6JywgZXJyKTtcbiAgICAgIGVycm9yKCdFcnJvcicsICdGYWlsZWQgdG8gYXNzaWduIGVtcGxveWVlcycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRBY3Rpb25Mb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUmVhc3NpZ25FbXBsb3llZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF0YXJnZXREZXBhcnRtZW50SWQpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRBY3Rpb25Mb2FkaW5nKHRydWUpO1xuICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoXG4gICAgICAgIHNlbGVjdGVkRW1wbG95ZWVzLm1hcChpZCA9PiBcbiAgICAgICAgICBhcGlTZXJ2aWNlLnVwZGF0ZUVtcGxveWVlKGlkLCB7IGRlcGFydG1lbnRfaWQ6IHRhcmdldERlcGFydG1lbnRJZCB9KVxuICAgICAgICApXG4gICAgICApO1xuICAgICAgc3VjY2VzcygnU3VjY2VzcycsIGAke3NlbGVjdGVkRW1wbG95ZWVzLmxlbmd0aH0gZW1wbG95ZWVzIHJlYXNzaWduZWQgc3VjY2Vzc2Z1bGx5YCk7XG4gICAgICBzZXRTZWxlY3RlZEVtcGxveWVlcyhbXSk7XG4gICAgICBzZXRTaG93UmVhc3NpZ25Nb2RhbChmYWxzZSk7XG4gICAgICBzZXRUYXJnZXREZXBhcnRtZW50SWQodW5kZWZpbmVkKTtcbiAgICAgIGZldGNoRGF0YSgpO1xuICAgICAgb25Bc3NpZ25tZW50Q2hhbmdlPy4oKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byByZWFzc2lnbiBlbXBsb3llZXM6JywgZXJyKTtcbiAgICAgIGVycm9yKCdFcnJvcicsICdGYWlsZWQgdG8gcmVhc3NpZ24gZW1wbG95ZWVzJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZW5kZXJFbXBsb3llZUxpc3QgPSAoZW1wbG95ZWVMaXN0OiBFbXBsb3llZVdpdGhEZXBhcnRtZW50W10sIHRpdGxlOiBzdHJpbmcsIHNob3dBY3Rpb25zOiBib29sZWFuID0gdHJ1ZSkgPT4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPnt0aXRsZX08L2gzPlxuICAgICAgICAgIHtzaG93QWN0aW9ucyAmJiBlbXBsb3llZUxpc3QubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3NlbGVjdGVkRW1wbG95ZWVzLmxlbmd0aCA9PT0gZW1wbG95ZWVMaXN0Lmxlbmd0aCAmJiBlbXBsb3llZUxpc3QubGVuZ3RoID4gMH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KCkgPT4gaGFuZGxlU2VsZWN0QWxsKGVtcGxveWVlTGlzdCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZCBib3JkZXItZ3JheS0zMDAgdGV4dC1ibHVlLTYwMCBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+U2VsZWN0IEFsbDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgICBcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LWgtNjQgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgIHtlbXBsb3llZUxpc3QubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IHRleHQtY2VudGVyIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgIE5vIGVtcGxveWVlcyBmb3VuZFxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGl2aWRlLXlcIj5cbiAgICAgICAgICAgIHtlbXBsb3llZUxpc3QubWFwKGVtcGxveWVlID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e2VtcGxveWVlLmlkfSBjbGFzc05hbWU9XCJwLTMgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGhvdmVyOmJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAge3Nob3dBY3Rpb25zICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZWxlY3RlZEVtcGxveWVlcy5pbmNsdWRlcyhlbXBsb3llZS5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eygpID0+IGhhbmRsZVNlbGVjdEVtcGxveWVlKGVtcGxveWVlLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkIGJvcmRlci1ncmF5LTMwMCB0ZXh0LWJsdWUtNjAwIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ibHVlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgIHtlbXBsb3llZS5waG90b191cmwgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtlbXBsb3llZS5waG90b191cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e2Ake2VtcGxveWVlLmZpcnN0X25hbWV9ICR7ZW1wbG95ZWUubGFzdF9uYW1lfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtlbXBsb3llZS5maXJzdF9uYW1lLmNoYXJBdCgwKX17ZW1wbG95ZWUubGFzdF9uYW1lLmNoYXJBdCgwKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7ZW1wbG95ZWUuZmlyc3RfbmFtZX0ge2VtcGxveWVlLmxhc3RfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj57ZW1wbG95ZWUucG9zaXRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxCYWRnZSBcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17ZW1wbG95ZWUuZW1wbG95bWVudF9zdGF0dXMgPT09ICdhY3RpdmUnID8gJ3N1Y2Nlc3MnIDogJ3dhcm5pbmcnfVxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7ZW1wbG95ZWUuZW1wbG95bWVudF9zdGF0dXMudG9VcHBlckNhc2UoKX1cbiAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjQgJHtjbGFzc05hbWV9YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgc3BhY2UteS02ICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+RW1wbG95ZWUgQXNzaWdubWVudDwvaDI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICB7c2VsZWN0ZWRFbXBsb3llZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dSZWFzc2lnbk1vZGFsKHRydWUpfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXthY3Rpb25Mb2FkaW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgUmVhc3NpZ24gU2VsZWN0ZWQgKHtzZWxlY3RlZEVtcGxveWVlcy5sZW5ndGh9KVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBc3NpZ25Nb2RhbCh0cnVlKX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17YWN0aW9uTG9hZGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIEFzc2lnbiBTZWxlY3RlZCAoe3NlbGVjdGVkRW1wbG95ZWVzLmxlbmd0aH0pXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgIHtyZW5kZXJFbXBsb3llZUxpc3QoZW1wbG95ZWVzLCBgQ3VycmVudCBEZXBhcnRtZW50IEVtcGxveWVlcyAoJHtlbXBsb3llZXMubGVuZ3RofSlgKX1cbiAgICAgICAge3JlbmRlckVtcGxveWVlTGlzdCh1bmFzc2lnbmVkRW1wbG95ZWVzLCBgVW5hc3NpZ25lZCBFbXBsb3llZXMgKCR7dW5hc3NpZ25lZEVtcGxveWVlcy5sZW5ndGh9KWApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBBc3NpZ24gQ29uZmlybWF0aW9uIE1vZGFsICovfVxuICAgICAgPENvbmZpcm1hdGlvbk1vZGFsXG4gICAgICAgIGlzT3Blbj17c2hvd0Fzc2lnbk1vZGFsfVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93QXNzaWduTW9kYWwoZmFsc2UpfVxuICAgICAgICBvbkNvbmZpcm09e2hhbmRsZUFzc2lnbkVtcGxveWVlc31cbiAgICAgICAgdGl0bGU9XCJBc3NpZ24gRW1wbG95ZWVzXCJcbiAgICAgICAgbWVzc2FnZT17YEFyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBhc3NpZ24gJHtzZWxlY3RlZEVtcGxveWVlcy5sZW5ndGh9IHNlbGVjdGVkIGVtcGxveWVlcyB0byB0aGlzIGRlcGFydG1lbnQ/YH1cbiAgICAgICAgY29uZmlybVRleHQ9XCJBc3NpZ25cIlxuICAgICAgICB0eXBlPVwiaW5mb1wiXG4gICAgICAgIGxvYWRpbmc9e2FjdGlvbkxvYWRpbmd9XG4gICAgICAvPlxuXG4gICAgICB7LyogUmVhc3NpZ24gTW9kYWwgKi99XG4gICAgICB7c2hvd1JlYXNzaWduTW9kYWwgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei01MCBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLXNjcmVlbiBwdC00IHB4LTQgcGItMjAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ncmF5LTUwMCBiZy1vcGFjaXR5LTc1XCIgb25DbGljaz17KCkgPT4gc2V0U2hvd1JlYXNzaWduTW9kYWwoZmFsc2UpfSAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgYWxpZ24tYm90dG9tIGJnLXdoaXRlIHJvdW5kZWQtbGcgdGV4dC1sZWZ0IG92ZXJmbG93LWhpZGRlbiBzaGFkb3cteGwgdHJhbnNmb3JtIHRyYW5zaXRpb24tYWxsIHNtOm15LTggc206YWxpZ24tbWlkZGxlIHNtOm1heC13LWxnIHNtOnctZnVsbFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHB4LTQgcHQtNSBwYi00IHNtOnAtNiBzbTpwYi00XCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgbGVhZGluZy02IGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgUmVhc3NpZ24gRW1wbG95ZWVzXG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgU2VsZWN0IHRoZSB0YXJnZXQgZGVwYXJ0bWVudCBmb3Ige3NlbGVjdGVkRW1wbG95ZWVzLmxlbmd0aH0gc2VsZWN0ZWQgZW1wbG95ZWVzOlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8RGVwYXJ0bWVudFRyZWVTZWxlY3RcbiAgICAgICAgICAgICAgICAgIGRlcGFydG1lbnRzPXtkZXBhcnRtZW50cy5maWx0ZXIoZCA9PiBkLmlkICE9PSBkZXBhcnRtZW50SWQpfVxuICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRJZD17dGFyZ2V0RGVwYXJ0bWVudElkfVxuICAgICAgICAgICAgICAgICAgb25TZWxlY3Q9eyhkZXB0KSA9PiBzZXRUYXJnZXREZXBhcnRtZW50SWQoZGVwdD8uaWQpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgVGFyZ2V0IERlcGFydG1lbnRcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcHgtNCBweS0zIHNtOnB4LTYgc206ZmxleCBzbTpmbGV4LXJvdy1yZXZlcnNlXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUmVhc3NpZ25FbXBsb3llZXN9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IXRhcmdldERlcGFydG1lbnRJZCB8fCBhY3Rpb25Mb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHNtOnctYXV0byBzbTptbC0zXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7YWN0aW9uTG9hZGluZyA/ICdSZWFzc2lnbmluZy4uLicgOiAnUmVhc3NpZ24nfVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgc2V0U2hvd1JlYXNzaWduTW9kYWwoZmFsc2UpO1xuICAgICAgICAgICAgICAgICAgICBzZXRUYXJnZXREZXBhcnRtZW50SWQodW5kZWZpbmVkKTtcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17YWN0aW9uTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTMgdy1mdWxsIHNtOm10LTAgc206dy1hdXRvXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgRW1wbG95ZWVBc3NpZ25tZW50O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJhcGlTZXJ2aWNlIiwiQnV0dG9uIiwiQmFkZ2UiLCJDb25maXJtYXRpb25Nb2RhbCIsInVzZVRvYXN0SGVscGVycyIsIkRlcGFydG1lbnRUcmVlU2VsZWN0IiwiRW1wbG95ZWVBc3NpZ25tZW50IiwiZGVwYXJ0bWVudElkIiwib25Bc3NpZ25tZW50Q2hhbmdlIiwiY2xhc3NOYW1lIiwic3VjY2VzcyIsImVycm9yIiwiZW1wbG95ZWVzIiwic2V0RW1wbG95ZWVzIiwidW5hc3NpZ25lZEVtcGxveWVlcyIsInNldFVuYXNzaWduZWRFbXBsb3llZXMiLCJkZXBhcnRtZW50cyIsInNldERlcGFydG1lbnRzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzZWxlY3RlZEVtcGxveWVlcyIsInNldFNlbGVjdGVkRW1wbG95ZWVzIiwic2hvd0Fzc2lnbk1vZGFsIiwic2V0U2hvd0Fzc2lnbk1vZGFsIiwic2hvd1JlYXNzaWduTW9kYWwiLCJzZXRTaG93UmVhc3NpZ25Nb2RhbCIsInRhcmdldERlcGFydG1lbnRJZCIsInNldFRhcmdldERlcGFydG1lbnRJZCIsImFjdGlvbkxvYWRpbmciLCJzZXRBY3Rpb25Mb2FkaW5nIiwiZmV0Y2hEYXRhIiwiZW1wbG95ZWVzUmVzIiwidW5hc3NpZ25lZFJlcyIsImRlcGFydG1lbnRzUmVzIiwiUHJvbWlzZSIsImFsbCIsImdldERlcGFydG1lbnRFbXBsb3llZXMiLCJnZXRFbXBsb3llZXMiLCJkZXBhcnRtZW50X2lkIiwibGltaXQiLCJnZXREZXBhcnRtZW50cyIsImVyciIsImNvbnNvbGUiLCJoYW5kbGVTZWxlY3RFbXBsb3llZSIsImVtcGxveWVlSWQiLCJwcmV2IiwiaW5jbHVkZXMiLCJmaWx0ZXIiLCJpZCIsImhhbmRsZVNlbGVjdEFsbCIsImVtcGxveWVlTGlzdCIsImFsbElkcyIsIm1hcCIsImVtcCIsImxlbmd0aCIsImhhbmRsZUFzc2lnbkVtcGxveWVlcyIsInVwZGF0ZUVtcGxveWVlIiwiaGFuZGxlUmVhc3NpZ25FbXBsb3llZXMiLCJ1bmRlZmluZWQiLCJyZW5kZXJFbXBsb3llZUxpc3QiLCJ0aXRsZSIsInNob3dBY3Rpb25zIiwiZGl2IiwiaDMiLCJpbnB1dCIsInR5cGUiLCJjaGVja2VkIiwib25DaGFuZ2UiLCJzcGFuIiwiZW1wbG95ZWUiLCJwaG90b191cmwiLCJpbWciLCJzcmMiLCJhbHQiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwiY2hhckF0IiwicCIsInBvc2l0aW9uIiwidmFyaWFudCIsImVtcGxveW1lbnRfc3RhdHVzIiwic2l6ZSIsInRvVXBwZXJDYXNlIiwiaDIiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJpc09wZW4iLCJvbkNsb3NlIiwib25Db25maXJtIiwibWVzc2FnZSIsImNvbmZpcm1UZXh0IiwiZCIsInNlbGVjdGVkSWQiLCJvblNlbGVjdCIsImRlcHQiLCJwbGFjZWhvbGRlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/EmployeeAssignment.tsx\n"));

/***/ })

});