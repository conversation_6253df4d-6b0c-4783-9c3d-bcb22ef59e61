'use client';

import React from 'react';
import { formatCurrency } from '@/lib/utils';

interface BudgetData {
  allocated: number;
  spent: number;
  remaining: number;
  categories?: {
    name: string;
    allocated: number;
    spent: number;
  }[];
}

interface BudgetChartProps {
  data: BudgetData;
  title?: string;
  className?: string;
}

const BudgetChart: React.FC<BudgetChartProps> = ({ 
  data, 
  title = "Budget Overview",
  className = "" 
}) => {
  const spentPercentage = data.allocated > 0 ? (data.spent / data.allocated) * 100 : 0;
  const remainingPercentage = 100 - spentPercentage;

  const getStatusColor = () => {
    if (spentPercentage >= 90) return 'text-red-600';
    if (spentPercentage >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressBarColor = () => {
    if (spentPercentage >= 90) return 'bg-red-500';
    if (spentPercentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className={`bg-white rounded-lg border p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      
      {/* Main Budget Overview */}
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-500">Allocated</p>
            <p className="text-lg font-semibold text-blue-600">
              {formatCurrency(data.allocated)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-500">Spent</p>
            <p className={`text-lg font-semibold ${getStatusColor()}`}>
              {formatCurrency(data.spent)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-500">Remaining</p>
            <p className="text-lg font-semibold text-gray-900">
              {formatCurrency(data.remaining)}
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Budget Utilization</span>
            <span className={`font-medium ${getStatusColor()}`}>
              {spentPercentage.toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
              style={{ width: `${Math.min(spentPercentage, 100)}%` }}
            />
          </div>
        </div>

        {/* Status Message */}
        <div className="text-sm">
          {spentPercentage >= 100 ? (
            <p className="text-red-600 font-medium">⚠️ Budget exceeded!</p>
          ) : spentPercentage >= 90 ? (
            <p className="text-red-600 font-medium">⚠️ Budget nearly exhausted</p>
          ) : spentPercentage >= 75 ? (
            <p className="text-yellow-600 font-medium">⚠️ Budget usage high</p>
          ) : (
            <p className="text-green-600 font-medium">✓ Budget on track</p>
          )}
        </div>
      </div>

      {/* Category Breakdown */}
      {data.categories && data.categories.length > 0 && (
        <div className="mt-6 pt-6 border-t">
          <h4 className="text-md font-medium text-gray-900 mb-3">Category Breakdown</h4>
          <div className="space-y-3">
            {data.categories.map((category, index) => {
              const categoryPercentage = category.allocated > 0 
                ? (category.spent / category.allocated) * 100 
                : 0;
              
              return (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-700">{category.name}</span>
                    <span className="text-gray-500">
                      {formatCurrency(category.spent)} / {formatCurrency(category.allocated)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="h-2 bg-blue-500 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(categoryPercentage, 100)}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default BudgetChart;

// Simple Budget Summary Component
interface BudgetSummaryProps {
  allocated: number;
  spent: number;
  className?: string;
}

export const BudgetSummary: React.FC<BudgetSummaryProps> = ({ 
  allocated, 
  spent, 
  className = "" 
}) => {
  const percentage = allocated > 0 ? (spent / allocated) * 100 : 0;
  const remaining = allocated - spent;

  const getStatusColor = () => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex justify-between items-center">
        <span className="text-sm text-gray-600">Budget</span>
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {percentage.toFixed(1)}% used
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${
            percentage >= 90 ? 'bg-red-500' : 
            percentage >= 75 ? 'bg-yellow-500' : 'bg-green-500'
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
      <div className="flex justify-between text-xs text-gray-500">
        <span>{formatCurrency(spent)} spent</span>
        <span>{formatCurrency(remaining)} remaining</span>
      </div>
    </div>
  );
};
