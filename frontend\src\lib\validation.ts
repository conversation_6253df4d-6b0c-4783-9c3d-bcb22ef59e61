// Form validation utilities

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  min?: number;
  max?: number;
  email?: boolean;
  phone?: boolean;
  custom?: (value: any) => string | null;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export class FormValidator {
  private schema: ValidationSchema;

  constructor(schema: ValidationSchema) {
    this.schema = schema;
  }

  validate(data: Record<string, any>): ValidationError[] {
    const errors: ValidationError[] = [];

    for (const [field, rule] of Object.entries(this.schema)) {
      const value = data[field];
      const fieldErrors = this.validateField(field, value, rule);
      errors.push(...fieldErrors);
    }

    return errors;
  }

  validateField(field: string, value: any, rule: ValidationRule): ValidationError[] {
    const errors: ValidationError[] = [];

    // Required validation
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push({ field, message: `${this.formatFieldName(field)} is required` });
      return errors; // Don't continue with other validations if required field is empty
    }

    // Skip other validations if value is empty and not required
    if (value === undefined || value === null || value === '') {
      return errors;
    }

    // String length validations
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        errors.push({ 
          field, 
          message: `${this.formatFieldName(field)} must be at least ${rule.minLength} characters long` 
        });
      }

      if (rule.maxLength && value.length > rule.maxLength) {
        errors.push({ 
          field, 
          message: `${this.formatFieldName(field)} must be no more than ${rule.maxLength} characters long` 
        });
      }

      // Pattern validation
      if (rule.pattern && !rule.pattern.test(value)) {
        errors.push({ 
          field, 
          message: `${this.formatFieldName(field)} format is invalid` 
        });
      }

      // Email validation
      if (rule.email && !this.isValidEmail(value)) {
        errors.push({ 
          field, 
          message: `${this.formatFieldName(field)} must be a valid email address` 
        });
      }

      // Phone validation
      if (rule.phone && !this.isValidPhone(value)) {
        errors.push({ 
          field, 
          message: `${this.formatFieldName(field)} must be a valid phone number` 
        });
      }
    }

    // Number validations
    if (typeof value === 'number' || !isNaN(Number(value))) {
      const numValue = Number(value);

      if (rule.min !== undefined && numValue < rule.min) {
        errors.push({ 
          field, 
          message: `${this.formatFieldName(field)} must be at least ${rule.min}` 
        });
      }

      if (rule.max !== undefined && numValue > rule.max) {
        errors.push({ 
          field, 
          message: `${this.formatFieldName(field)} must be no more than ${rule.max}` 
        });
      }
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value);
      if (customError) {
        errors.push({ field, message: customError });
      }
    }

    return errors;
  }

  private formatFieldName(field: string): string {
    return field
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPhone(phone: string): boolean {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    // Check if it's a valid length (10-15 digits)
    return cleaned.length >= 10 && cleaned.length <= 15;
  }
}

// Predefined validation schemas
export const employeeValidationSchema: ValidationSchema = {
  employee_id: {
    required: true,
    minLength: 3,
    maxLength: 20,
    pattern: /^[A-Za-z0-9-_]+$/
  },
  first_name: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[A-Za-z\s]+$/
  },
  last_name: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[A-Za-z\s]+$/
  },
  middle_name: {
    maxLength: 50,
    pattern: /^[A-Za-z\s]*$/
  },
  email: {
    email: true,
    maxLength: 100
  },
  phone: {
    phone: true
  },
  emergency_contact_phone: {
    phone: true
  },
  department_id: {
    required: true,
    min: 1
  },
  position: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  hire_date: {
    required: true,
    custom: (value) => {
      if (value && new Date(value) > new Date()) {
        return 'Hire date cannot be in the future';
      }
      return null;
    }
  },
  termination_date: {
    custom: (value) => {
      if (value && new Date(value) > new Date()) {
        return 'Termination date cannot be in the future';
      }
      return null;
    }
  },
  salary: {
    min: 0,
    max: 10000000
  },
  hourly_rate: {
    min: 0,
    max: 1000
  }
};

export const departmentValidationSchema: ValidationSchema = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  code: {
    required: true,
    minLength: 2,
    maxLength: 10,
    pattern: /^[A-Z0-9-_]+$/
  },
  description: {
    maxLength: 500
  },
  budget: {
    min: 0,
    max: 1000000000
  },
  email: {
    email: true,
    maxLength: 100
  },
  phone: {
    phone: true
  },
  location: {
    maxLength: 200
  }
};

// Utility functions for form validation
export const validateForm = (data: Record<string, any>, schema: ValidationSchema): ValidationError[] => {
  const validator = new FormValidator(schema);
  return validator.validate(data);
};

export const hasValidationErrors = (errors: ValidationError[]): boolean => {
  return errors.length > 0;
};

export const getFieldError = (errors: ValidationError[], field: string): string | null => {
  const error = errors.find(err => err.field === field);
  return error ? error.message : null;
};

export const formatValidationErrors = (errors: ValidationError[]): string => {
  return errors.map(err => err.message).join(', ');
};
