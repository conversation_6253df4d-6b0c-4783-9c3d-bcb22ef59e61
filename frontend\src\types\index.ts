// User types
export interface User {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'hr' | 'department_head' | 'employee';
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile extends User {
  employee?: {
    id: number;
    employee_id: string;
    first_name: string;
    last_name: string;
    position: string;
    department_name: string;
  };
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  expires_in: string;
}

// Employee types
export interface Employee {
  id: number;
  employee_id: string;
  user_id?: number;
  
  // Personal Information
  first_name: string;
  last_name: string;
  middle_name?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;
  
  // Contact Information
  email?: string;
  phone?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  
  // Address
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  
  // Employment Details
  department_id: number;
  position: string;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
  employment_status: 'active' | 'inactive' | 'terminated' | 'resigned' | 'retired';
  hire_date: string;
  termination_date?: string;
  salary?: number;
  hourly_rate?: number;
  
  // Documents and Media
  photo_url?: string;
  resume_url?: string;
  contract_url?: string;
  
  // System Fields
  created_at: string;
  updated_at: string;
}

export interface EmployeeWithDepartment extends Employee {
  department_name: string;
  department_code: string;
  department_head_name?: string;
}

export interface CreateEmployeeRequest {
  employee_id: string;
  user_id?: number;
  first_name: string;
  last_name: string;
  middle_name?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;
  email?: string;
  phone?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  department_id: number;
  position: string;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
  employment_status?: 'active' | 'inactive' | 'terminated' | 'resigned' | 'retired';
  hire_date: string;
  termination_date?: string;
  salary?: number;
  hourly_rate?: number;
}

// Department types
export interface Department {
  id: number;
  name: string;
  code: string;
  description?: string;
  department_head_id?: number;
  parent_department_id?: number;
  budget: number;
  location?: string;
  phone?: string;
  email?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DepartmentWithStats extends Department {
  department_head_name?: string;
  parent_department_name?: string;
  total_employees: number;
  active_employees: number;
  inactive_employees: number;
  average_salary?: number;
  child_departments?: DepartmentWithStats[];
}

export interface CreateDepartmentRequest {
  name: string;
  code: string;
  description?: string;
  department_head_id?: number;
  parent_department_id?: number;
  budget?: number;
  location?: string;
  phone?: string;
  email?: string;
  is_active?: boolean;
}

// API Response types
export interface ApiResponse<T = any> {
  message?: string;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface EmployeeListResponse extends PaginatedResponse<EmployeeWithDepartment> {
  employees: EmployeeWithDepartment[];
}

export interface DepartmentListResponse extends PaginatedResponse<DepartmentWithStats> {
  departments: DepartmentWithStats[];
}

// Filter and search types
export interface EmployeeFilters {
  department_id?: number;
  employment_status?: string;
  employment_type?: string;
  position?: string;
  search?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

export interface DepartmentFilters {
  is_active?: boolean;
  search?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: { value: string | number; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

// Navigation types
export interface NavItem {
  name: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  current?: boolean;
  children?: NavItem[];
}

// Table types
export interface TableColumn<T = any> {
  key: keyof T | string;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  className?: string;
}

export interface TableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  onSort?: (column: string, direction: 'ASC' | 'DESC') => void;
  onRowClick?: (row: T) => void;
  className?: string;
}

// Modal types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// Toast types
export interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

// Auth context types
export interface AuthContextType {
  user: UserProfile | null;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  isAuthenticated: boolean;
}
