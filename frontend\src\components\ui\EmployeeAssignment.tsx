'use client';

import React, { useState, useEffect } from 'react';
import { EmployeeWithDepartment, DepartmentWithStats } from '@/types';
import { apiService } from '@/services/api';
import Button from './Button';
import Badge from './Badge';
import { ConfirmationModal } from './Modal';
import { useToastHelpers } from './Toast';
import { DepartmentTreeSelect } from './DepartmentHierarchy';

interface EmployeeAssignmentProps {
  departmentId: number;
  onAssignmentChange?: () => void;
  className?: string;
}

const EmployeeAssignment: React.FC<EmployeeAssignmentProps> = ({
  departmentId,
  onAssignmentChange,
  className = ""
}) => {
  const { success, error } = useToastHelpers();
  const [employees, setEmployees] = useState<EmployeeWithDepartment[]>([]);
  const [unassignedEmployees, setUnassignedEmployees] = useState<EmployeeWithDepartment[]>([]);
  const [departments, setDepartments] = useState<DepartmentWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [showReassignModal, setShowReassignModal] = useState(false);
  const [targetDepartmentId, setTargetDepartmentId] = useState<number | undefined>();
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchData();
  }, [departmentId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [employeesRes, unassignedRes, departmentsRes] = await Promise.all([
        apiService.getDepartmentEmployees(departmentId),
        apiService.getEmployees({ department_id: 'null', limit: 100 }),
        apiService.getDepartments({ limit: 100 })
      ]);

      setEmployees((employeesRes as any).employees || []);
      setUnassignedEmployees((unassignedRes as any).employees || []);
      setDepartments((departmentsRes as any).departments || []);
    } catch (err) {
      console.error('Failed to fetch data:', err);
      error('Error', 'Failed to load employee assignment data');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectEmployee = (employeeId: number) => {
    setSelectedEmployees(prev => 
      prev.includes(employeeId) 
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    );
  };

  const handleSelectAll = (employeeList: EmployeeWithDepartment[]) => {
    const allIds = employeeList.map(emp => emp.id);
    if (selectedEmployees.length === allIds.length) {
      setSelectedEmployees([]);
    } else {
      setSelectedEmployees(allIds);
    }
  };

  const handleAssignEmployees = async () => {
    try {
      setActionLoading(true);
      await Promise.all(
        selectedEmployees.map(id => 
          apiService.updateEmployee(id, { department_id: departmentId })
        )
      );
      success('Success', `${selectedEmployees.length} employees assigned successfully`);
      setSelectedEmployees([]);
      setShowAssignModal(false);
      fetchData();
      onAssignmentChange?.();
    } catch (err) {
      console.error('Failed to assign employees:', err);
      error('Error', 'Failed to assign employees');
    } finally {
      setActionLoading(false);
    }
  };

  const handleReassignEmployees = async () => {
    if (!targetDepartmentId) return;

    try {
      setActionLoading(true);
      await Promise.all(
        selectedEmployees.map(id => 
          apiService.updateEmployee(id, { department_id: targetDepartmentId })
        )
      );
      success('Success', `${selectedEmployees.length} employees reassigned successfully`);
      setSelectedEmployees([]);
      setShowReassignModal(false);
      setTargetDepartmentId(undefined);
      fetchData();
      onAssignmentChange?.();
    } catch (err) {
      console.error('Failed to reassign employees:', err);
      error('Error', 'Failed to reassign employees');
    } finally {
      setActionLoading(false);
    }
  };

  const renderEmployeeList = (employeeList: EmployeeWithDepartment[], title: string, showActions: boolean = true) => (
    <div className="bg-white rounded-lg border">
      <div className="p-4 border-b">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          {showActions && employeeList.length > 0 && (
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedEmployees.length === employeeList.length && employeeList.length > 0}
                onChange={() => handleSelectAll(employeeList)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-500">Select All</span>
            </div>
          )}
        </div>
      </div>
      
      <div className="max-h-64 overflow-y-auto">
        {employeeList.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            No employees found
          </div>
        ) : (
          <div className="divide-y">
            {employeeList.map(employee => (
              <div key={employee.id} className="p-3 flex items-center justify-between hover:bg-gray-50">
                <div className="flex items-center space-x-3">
                  {showActions && (
                    <input
                      type="checkbox"
                      checked={selectedEmployees.includes(employee.id)}
                      onChange={() => handleSelectEmployee(employee.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  )}
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center overflow-hidden">
                    {employee.photo_url ? (
                      <img
                        src={employee.photo_url}
                        alt={`${employee.first_name} ${employee.last_name}`}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-sm font-medium text-blue-800">
                        {employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
                      </span>
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {employee.first_name} {employee.last_name}
                    </p>
                    <p className="text-sm text-gray-500">{employee.position}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Badge 
                    variant={employee.employment_status === 'active' ? 'success' : 'warning'}
                    size="sm"
                  >
                    {employee.employment_status.toUpperCase()}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Employee Assignment</h2>
        <div className="flex space-x-2">
          {selectedEmployees.length > 0 && (
            <>
              <Button
                variant="outline"
                onClick={() => setShowReassignModal(true)}
                disabled={actionLoading}
              >
                Reassign Selected ({selectedEmployees.length})
              </Button>
              <Button
                onClick={() => setShowAssignModal(true)}
                disabled={actionLoading}
              >
                Assign Selected ({selectedEmployees.length})
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderEmployeeList(employees, `Current Department Employees (${employees.length})`)}
        {renderEmployeeList(unassignedEmployees, `Unassigned Employees (${unassignedEmployees.length})`)}
      </div>

      {/* Assign Confirmation Modal */}
      <ConfirmationModal
        isOpen={showAssignModal}
        onClose={() => setShowAssignModal(false)}
        onConfirm={handleAssignEmployees}
        title="Assign Employees"
        message={`Are you sure you want to assign ${selectedEmployees.length} selected employees to this department?`}
        confirmText="Assign"
        type="info"
        loading={actionLoading}
      />

      {/* Reassign Modal */}
      {showReassignModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={() => setShowReassignModal(false)} />
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Reassign Employees
                </h3>
                <p className="text-sm text-gray-500 mb-4">
                  Select the target department for {selectedEmployees.length} selected employees:
                </p>
                <DepartmentTreeSelect
                  departments={departments.filter(d => d.id !== departmentId)}
                  selectedId={targetDepartmentId}
                  onSelect={(dept) => setTargetDepartmentId(dept?.id)}
                  placeholder="Select Target Department"
                />
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <Button
                  onClick={handleReassignEmployees}
                  disabled={!targetDepartmentId || actionLoading}
                  className="w-full sm:w-auto sm:ml-3"
                >
                  {actionLoading ? 'Reassigning...' : 'Reassign'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowReassignModal(false);
                    setTargetDepartmentId(undefined);
                  }}
                  disabled={actionLoading}
                  className="mt-3 w-full sm:mt-0 sm:w-auto"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeeAssignment;
