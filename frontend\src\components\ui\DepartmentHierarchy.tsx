'use client';

import React, { useState } from 'react';
import { DepartmentWithStats } from '@/types';
import Badge from './Badge';
import { formatCurrency } from '@/lib/utils';

interface DepartmentHierarchyProps {
  departments: DepartmentWithStats[];
  onDepartmentClick?: (department: DepartmentWithStats) => void;
  className?: string;
}

const DepartmentHierarchy: React.FC<DepartmentHierarchyProps> = ({
  departments,
  onDepartmentClick,
  className = ""
}) => {
  const [expandedDepartments, setExpandedDepartments] = useState<Set<number>>(new Set());

  // Build hierarchy tree
  const buildHierarchy = (depts: DepartmentWithStats[]): DepartmentWithStats[] => {
    const departmentMap = new Map<number, DepartmentWithStats>();
    const rootDepartments: DepartmentWithStats[] = [];

    // Create a map of all departments
    depts.forEach(dept => {
      departmentMap.set(dept.id, { ...dept, child_departments: [] });
    });

    // Build the hierarchy
    depts.forEach(dept => {
      const department = departmentMap.get(dept.id)!;
      
      if (dept.parent_department_id) {
        const parent = departmentMap.get(dept.parent_department_id);
        if (parent) {
          parent.child_departments = parent.child_departments || [];
          parent.child_departments.push(department);
        } else {
          // Parent not found, treat as root
          rootDepartments.push(department);
        }
      } else {
        rootDepartments.push(department);
      }
    });

    return rootDepartments;
  };

  const toggleExpanded = (departmentId: number) => {
    const newExpanded = new Set(expandedDepartments);
    if (newExpanded.has(departmentId)) {
      newExpanded.delete(departmentId);
    } else {
      newExpanded.add(departmentId);
    }
    setExpandedDepartments(newExpanded);
  };

  const renderDepartment = (department: DepartmentWithStats, level: number = 0) => {
    const hasChildren = department.child_departments && department.child_departments.length > 0;
    const isExpanded = expandedDepartments.has(department.id);
    const indentClass = level > 0 ? `ml-${level * 6}` : '';

    return (
      <div key={department.id} className="space-y-2">
        <div 
          className={`flex items-center justify-between p-3 bg-white border rounded-lg hover:shadow-sm transition-shadow cursor-pointer ${indentClass}`}
          onClick={() => onDepartmentClick?.(department)}
        >
          <div className="flex items-center space-x-3">
            {hasChildren && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleExpanded(department.id);
                }}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <svg 
                  className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            )}
            
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <div>
                <h3 className="font-medium text-gray-900">{department.name}</h3>
                <p className="text-sm text-gray-500">{department.code}</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">
                {department.total_employees} employees
              </p>
              <p className="text-xs text-gray-500">
                {formatCurrency(department.budget)} budget
              </p>
            </div>
            
            <Badge 
              variant={department.is_active ? "success" : "danger"} 
              size="sm"
            >
              {department.is_active ? 'Active' : 'Inactive'}
            </Badge>
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div className="space-y-2">
            {department.child_departments!.map(child => 
              renderDepartment(child, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  const hierarchyData = buildHierarchy(departments);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Department Hierarchy</h3>
        <div className="flex space-x-2">
          <button
            onClick={() => setExpandedDepartments(new Set(departments.map(d => d.id)))}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Expand All
          </button>
          <button
            onClick={() => setExpandedDepartments(new Set())}
            className="text-sm text-gray-600 hover:text-gray-800"
          >
            Collapse All
          </button>
        </div>
      </div>
      
      <div className="space-y-2">
        {hierarchyData.map(department => renderDepartment(department))}
      </div>
      
      {hierarchyData.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No departments found</p>
        </div>
      )}
    </div>
  );
};

export default DepartmentHierarchy;

// Simple Department Tree Component for selection
interface DepartmentTreeSelectProps {
  departments: DepartmentWithStats[];
  selectedId?: number;
  onSelect: (department: DepartmentWithStats | null) => void;
  placeholder?: string;
  className?: string;
}

export const DepartmentTreeSelect: React.FC<DepartmentTreeSelectProps> = ({
  departments,
  selectedId,
  onSelect,
  placeholder = "Select Department",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const buildHierarchy = (depts: DepartmentWithStats[]): DepartmentWithStats[] => {
    const departmentMap = new Map<number, DepartmentWithStats>();
    const rootDepartments: DepartmentWithStats[] = [];

    depts.forEach(dept => {
      departmentMap.set(dept.id, { ...dept, child_departments: [] });
    });

    depts.forEach(dept => {
      const department = departmentMap.get(dept.id)!;
      
      if (dept.parent_department_id) {
        const parent = departmentMap.get(dept.parent_department_id);
        if (parent) {
          parent.child_departments = parent.child_departments || [];
          parent.child_departments.push(department);
        } else {
          rootDepartments.push(department);
        }
      } else {
        rootDepartments.push(department);
      }
    });

    return rootDepartments;
  };

  const renderOption = (department: DepartmentWithStats, level: number = 0) => {
    const indent = '  '.repeat(level);
    
    return (
      <div key={department.id}>
        <button
          className="w-full text-left px-3 py-2 hover:bg-gray-100 text-sm"
          onClick={() => {
            onSelect(department);
            setIsOpen(false);
          }}
        >
          {indent}{department.name} ({department.code})
        </button>
        {department.child_departments?.map(child => 
          renderOption(child, level + 1)
        )}
      </div>
    );
  };

  const selectedDepartment = departments.find(d => d.id === selectedId);
  const hierarchyData = buildHierarchy(departments);

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-left focus:outline-none focus:ring-2 focus:ring-blue-500"
        onClick={() => setIsOpen(!isOpen)}
      >
        {selectedDepartment ? `${selectedDepartment.name} (${selectedDepartment.code})` : placeholder}
        <svg className="w-4 h-4 float-right mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
          <button
            className="w-full text-left px-3 py-2 hover:bg-gray-100 text-sm text-gray-500"
            onClick={() => {
              onSelect(null);
              setIsOpen(false);
            }}
          >
            {placeholder}
          </button>
          {hierarchyData.map(department => renderOption(department))}
        </div>
      )}
    </div>
  );
};
